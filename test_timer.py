#!/usr/bin/env python3
"""
Test script for the presentation timer
This script tests the basic functionality without GUI
"""

import sys
import time
from unittest.mock import Mock

# Mock tkinter for testing
sys.modules['tkinter'] = Mock()
sys.modules['tkinter.ttk'] = Mock()

def test_timer_logic():
    """Test basic timer logic"""
    print("Testing Presentation Timer Logic...")
    
    # Test segment duration validation
    test_durations = [5.0, 3.5, 10.0, 2.5, 4.0, 1.5]
    print(f"✓ Test durations: {test_durations}")
    
    # Test time conversion
    total_seconds = sum(duration * 60 for duration in test_durations)
    total_minutes = total_seconds / 60
    print(f"✓ Total presentation time: {total_minutes} minutes ({total_seconds} seconds)")
    
    # Test time formatting
    for duration in test_durations:
        seconds = int(duration * 60)
        minutes = seconds // 60
        secs = seconds % 60
        formatted = f"{minutes:02d}:{secs:02d}"
        print(f"✓ {duration} min = {formatted}")
    
    print("\n✅ All timer logic tests passed!")

def test_segment_labels():
    """Test segment label functionality"""
    print("\nTesting Segment Labels...")
    
    default_labels = ["Opening", "Introduction", "Main Content", "Discussion", "Q&A", "Closing"]
    
    for i, label in enumerate(default_labels):
        print(f"✓ Segment {i+1}: {label}")
    
    print("\n✅ Segment labels test passed!")

def test_audio_system():
    """Test audio system availability"""
    print("\nTesting Audio System...")
    
    import platform
    system = platform.system()
    print(f"✓ Detected system: {system}")
    
    if system == "Windows":
        try:
            import winsound
            print("✓ Windows audio (winsound) available")
        except ImportError:
            print("⚠ Windows audio not available")
    else:
        print("✓ Cross-platform audio will use system commands")
    
    print("\n✅ Audio system test completed!")

if __name__ == "__main__":
    print("🚀 Starting Presentation Timer Tests\n")
    print("=" * 50)
    
    test_timer_logic()
    test_segment_labels()
    test_audio_system()
    
    print("\n" + "=" * 50)
    print("🎉 All tests completed successfully!")
    print("\nTo run the actual timer application, use:")
    print("python presentation_timer.py")
