<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Simple Presentation Timer</title>
    <style>
        .simple-timer * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        .simple-timer {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif !important;
            background: #121212 !important;
            padding: 20px !important;
            color: #FFFFFF !important;
            border-radius: 12px !important;
            max-width: 400px !important;
            width: 100% !important;
            margin: 0 auto !important;
            border: 1px solid #333333 !important;
        }

        .simple-timer h1 {
            color: #FFFFFF !important;
            font-size: 24px !important;
            font-weight: 600 !important;
            margin-bottom: 20px !important;
            text-align: center !important;
        }

        .simple-timer .settings {
            background: #2A2A2A !important;
            padding: 16px !important;
            border-radius: 8px !important;
            margin-bottom: 20px !important;
            border: 1px solid #404040 !important;
        }

        .simple-timer .settings h2 {
            color: #FFFFFF !important;
            font-size: 16px !important;
            margin-bottom: 12px !important;
        }

        .simple-timer .audio-setting {
            display: flex !important;
            justify-content: space-between !important;
            align-items: center !important;
        }

        .simple-timer .audio-setting span {
            color: #B3B3B3 !important;
            font-size: 14px !important;
        }

        .simple-timer .toggle {
            width: 50px !important;
            height: 24px !important;
            background: #404040 !important;
            border-radius: 12px !important;
            position: relative !important;
            cursor: pointer !important;
            transition: background 0.3s !important;
        }

        .simple-timer .toggle.active {
            background: #007bff !important;
        }

        .simple-timer .toggle-ball {
            width: 16px !important;
            height: 16px !important;
            background: #B3B3B3 !important;
            border-radius: 50% !important;
            position: absolute !important;
            top: 4px !important;
            left: 4px !important;
            transition: all 0.3s !important;
        }

        .simple-timer .toggle.active .toggle-ball {
            left: 30px !important;
            background: #FFFFFF !important;
        }

        .simple-timer .segments {
            margin-bottom: 20px !important;
        }

        .simple-timer .segment {
            display: flex !important;
            align-items: center !important;
            gap: 12px !important;
            margin-bottom: 12px !important;
        }

        .simple-timer .segment input[type="text"] {
            flex: 1 !important;
            padding: 8px 12px !important;
            border: 2px solid #404040 !important;
            border-radius: 6px !important;
            background: #2A2A2A !important;
            color: #FFFFFF !important;
            font-size: 14px !important;
        }

        .simple-timer .segment input[type="text"]:focus {
            outline: none !important;
            border-color: #007bff !important;
        }

        .simple-timer .segment input[type="number"] {
            width: 80px !important;
            padding: 8px 12px !important;
            border: 2px solid #404040 !important;
            border-radius: 6px !important;
            background: #2A2A2A !important;
            color: #FFFFFF !important;
            font-size: 14px !important;
            text-align: center !important;
        }

        .simple-timer .segment input[type="number"]:focus {
            outline: none !important;
            border-color: #007bff !important;
        }

        .simple-timer .segment span {
            color: #B3B3B3 !important;
            font-size: 14px !important;
        }

        .simple-timer .display {
            background: #2A2A2A !important;
            padding: 20px !important;
            border-radius: 8px !important;
            text-align: center !important;
            margin-bottom: 20px !important;
            border: 2px solid #404040 !important;
        }

        .simple-timer .current-segment {
            color: #B3B3B3 !important;
            font-size: 16px !important;
            margin-bottom: 8px !important;
        }

        .simple-timer .time {
            color: #FFFFFF !important;
            font-size: 48px !important;
            font-weight: 700 !important;
            margin-bottom: 12px !important;
        }

        .simple-timer .time.warning {
            color: #dc3545 !important;
        }

        .simple-timer .progress {
            width: 100% !important;
            height: 8px !important;
            background: #404040 !important;
            border-radius: 4px !important;
            overflow: hidden !important;
            margin-bottom: 12px !important;
        }

        .simple-timer .progress-bar {
            height: 100% !important;
            background: linear-gradient(90deg, #007bff, #0056b3) !important;
            width: 0% !important;
            transition: width 0.3s !important;
        }

        .simple-timer .progress-bar.warning {
            background: linear-gradient(90deg, #dc3545, #c82333) !important;
        }

        .simple-timer .dots {
            display: flex !important;
            gap: 4px !important;
        }

        .simple-timer .dot {
            flex: 1 !important;
            height: 6px !important;
            background: #404040 !important;
            border-radius: 3px !important;
        }

        .simple-timer .dot.completed {
            background: #28a745 !important;
        }

        .simple-timer .dot.active {
            background: #007bff !important;
        }

        .simple-timer .controls {
            display: flex !important;
            gap: 12px !important;
            margin-bottom: 16px !important;
        }

        .simple-timer .btn {
            flex: 1 !important;
            padding: 12px 16px !important;
            border: none !important;
            border-radius: 6px !important;
            font-size: 14px !important;
            font-weight: 600 !important;
            cursor: pointer !important;
            text-transform: uppercase !important;
            transition: all 0.2s !important;
        }

        .simple-timer .btn-primary {
            background: #007bff !important;
            color: white !important;
        }

        .simple-timer .btn-primary:hover {
            background: #0056b3 !important;
        }

        .simple-timer .btn-secondary {
            background: #6c757d !important;
            color: white !important;
        }

        .simple-timer .btn-secondary:hover {
            background: #545b62 !important;
        }

        .simple-timer .notification {
            padding: 12px !important;
            border-radius: 6px !important;
            margin-bottom: 16px !important;
            text-align: center !important;
            display: none !important;
        }

        .simple-timer .notification.success {
            background: #1a4d3a !important;
            color: #4ade80 !important;
            border: 1px solid #22c55e !important;
        }

        .simple-timer .notification.warning {
            background: #4d3319 !important;
            color: #fbbf24 !important;
            border: 1px solid #f59e0b !important;
        }

        .simple-timer .notification.show {
            display: block !important;
        }

        @media (max-width: 480px) {
            .simple-timer {
                padding: 16px !important;
                margin: 10px !important;
            }

            .simple-timer .time {
                font-size: 36px !important;
            }

            .simple-timer .controls {
                flex-direction: column !important;
            }
        }
    </style>
</head>
<body>
    <div class="simple-timer">
        <h1>Presentation Timer</h1>

        <div class="settings">
            <h2>Settings</h2>
            <div class="audio-setting">
                <span>Audio Alerts</span>
                <div class="toggle active" onclick="toggleAudio()">
                    <div class="toggle-ball"></div>
                </div>
            </div>
        </div>

        <div class="segments">
            <div class="segment">
                <input type="text" value="Introduction" maxlength="20" onchange="saveData()">
                <input type="number" value="5" min="0.1" max="60" step="0.1" onchange="saveData()">
                <span>min</span>
            </div>
            <div class="segment">
                <input type="text" value="Main Content" maxlength="20" onchange="saveData()">
                <input type="number" value="15" min="0.1" max="60" step="0.1" onchange="saveData()">
                <span>min</span>
            </div>
            <div class="segment">
                <input type="text" value="Q&A Session" maxlength="20" onchange="saveData()">
                <input type="number" value="8" min="0.1" max="60" step="0.1" onchange="saveData()">
                <span>min</span>
            </div>
            <div class="segment">
                <input type="text" value="Conclusion" maxlength="20" onchange="saveData()">
                <input type="number" value="2" min="0.1" max="60" step="0.1" onchange="saveData()">
                <span>min</span>
            </div>
        </div>

        <div class="notification" id="notification"></div>

        <div class="display">
            <div class="current-segment" id="currentSegment">Ready to start</div>
            <div class="time" id="timeDisplay">00:00</div>
            <div class="progress">
                <div class="progress-bar" id="progressBar"></div>
            </div>
            <div class="dots">
                <div class="dot" id="dot1"></div>
                <div class="dot" id="dot2"></div>
                <div class="dot" id="dot3"></div>
                <div class="dot" id="dot4"></div>
            </div>
        </div>

        <div class="controls">
            <button class="btn btn-primary" onclick="toggleTimer()" id="startBtn">Start</button>
            <button class="btn btn-secondary" onclick="resetTimer()">Reset</button>
        </div>
    </div>

    <script>
        // Global variables for WordPress compatibility
        var timerState = {
            isRunning: false,
            isPaused: false,
            currentSegment: 0,
            timeRemaining: 0,
            segments: [],
            labels: ['Introduction', 'Main Content', 'Q&A Session', 'Conclusion'],
            audioEnabled: true,
            interval: null
        };

        // Audio toggle function
        function toggleAudio() {
            var toggle = document.querySelector('.toggle');
            timerState.audioEnabled = !timerState.audioEnabled;

            if (timerState.audioEnabled) {
                toggle.classList.add('active');
                showNotification('Audio alerts enabled');
            } else {
                toggle.classList.remove('active');
                showNotification('Audio alerts disabled');
            }
            saveData();
        }

        // Get segment data
        function getSegmentData() {
            var segments = document.querySelectorAll('.segment');
            var data = [];

            for (var i = 0; i < segments.length; i++) {
                var textInput = segments[i].querySelector('input[type="text"]');
                var numberInput = segments[i].querySelector('input[type="number"]');

                var minutes = parseFloat(numberInput.value) || 0;
                var seconds = Math.round((minutes * 60) / 5) * 5; // Round to 5 seconds

                data.push({
                    label: textInput.value.trim() || timerState.labels[i],
                    seconds: seconds
                });
            }

            return data;
        }

        // Save data to localStorage
        function saveData() {
            try {
                var data = getSegmentData();
                var saveObj = {
                    segments: data,
                    audioEnabled: timerState.audioEnabled
                };
                localStorage.setItem('simpleTimer', JSON.stringify(saveObj));
            } catch (e) {
                console.log('Save failed:', e);
            }
        }

        // Load data from localStorage
        function loadData() {
            try {
                var saved = localStorage.getItem('simpleTimer');
                if (saved) {
                    var data = JSON.parse(saved);

                    if (data.audioEnabled !== undefined) {
                        timerState.audioEnabled = data.audioEnabled;
                        var toggle = document.querySelector('.toggle');
                        if (timerState.audioEnabled) {
                            toggle.classList.add('active');
                        } else {
                            toggle.classList.remove('active');
                        }
                    }

                    if (data.segments) {
                        var segments = document.querySelectorAll('.segment');
                        for (var i = 0; i < segments.length && i < data.segments.length; i++) {
                            var textInput = segments[i].querySelector('input[type="text"]');
                            var numberInput = segments[i].querySelector('input[type="number"]');

                            if (data.segments[i].label) {
                                textInput.value = data.segments[i].label;
                            }
                            if (data.segments[i].seconds) {
                                numberInput.value = (data.segments[i].seconds / 60).toFixed(1);
                            }
                        }
                    }
                }
            } catch (e) {
                console.log('Load failed:', e);
            }
        }

        // Format time display
        function formatTime(seconds) {
            var mins = Math.floor(seconds / 60);
            var secs = seconds % 60;
            return (mins < 10 ? '0' : '') + mins + ':' + (secs < 10 ? '0' : '') + secs;
        }

        // Show notification
        function showNotification(message, type) {
            var notification = document.getElementById('notification');
            notification.textContent = message;
            notification.className = 'notification ' + (type || 'success') + ' show';

            setTimeout(function() {
                notification.classList.remove('show');
            }, 3000);
        }

        // Play audio alert
        function playAlert() {
            if (!timerState.audioEnabled) return;

            try {
                var audioContext = new (window.AudioContext || window.webkitAudioContext)();
                var oscillator = audioContext.createOscillator();
                var gainNode = audioContext.createGain();

                oscillator.connect(gainNode);
                gainNode.connect(audioContext.destination);

                oscillator.frequency.value = 800;
                oscillator.type = 'sine';

                gainNode.gain.setValueAtTime(0.3, audioContext.currentTime);
                gainNode.gain.exponentialRampToValueAtTime(0.01, audioContext.currentTime + 0.5);

                oscillator.start(audioContext.currentTime);
                oscillator.stop(audioContext.currentTime + 0.5);
            } catch (e) {
                console.log('Audio not supported');
            }
        }

        // Update display
        function updateDisplay() {
            var currentSegmentEl = document.getElementById('currentSegment');
            var timeDisplay = document.getElementById('timeDisplay');
            var progressBar = document.getElementById('progressBar');

            if (!timerState.isRunning && !timerState.isPaused) {
                currentSegmentEl.textContent = 'Ready to start';
                timeDisplay.textContent = '00:00';
                progressBar.style.width = '0%';
                timeDisplay.classList.remove('warning');
                progressBar.classList.remove('warning');
            } else {
                var data = getSegmentData();
                currentSegmentEl.textContent = data[timerState.currentSegment].label;
                timeDisplay.textContent = formatTime(timerState.timeRemaining);

                var currentSegmentTime = data[timerState.currentSegment].seconds;
                var progress = ((currentSegmentTime - timerState.timeRemaining) / currentSegmentTime) * 100;
                progressBar.style.width = Math.max(0, Math.min(100, progress)) + '%';

                // Warning when less than 30 seconds
                if (timerState.timeRemaining <= 30 && timerState.timeRemaining > 0) {
                    timeDisplay.classList.add('warning');
                    progressBar.classList.add('warning');
                } else {
                    timeDisplay.classList.remove('warning');
                    progressBar.classList.remove('warning');
                }
            }

            // Update dots
            for (var i = 1; i <= 4; i++) {
                var dot = document.getElementById('dot' + i);
                dot.className = 'dot';
                if (i - 1 < timerState.currentSegment) {
                    dot.classList.add('completed');
                } else if (i - 1 === timerState.currentSegment && timerState.isRunning) {
                    dot.classList.add('active');
                }
            }
        }

        // Timer tick function
        function tick() {
            timerState.timeRemaining--;

            if (timerState.timeRemaining <= 0) {
                playAlert();

                var data = getSegmentData();
                if (timerState.currentSegment < data.length - 1) {
                    showNotification(data[timerState.currentSegment].label + ' completed!');
                    timerState.currentSegment++;
                    timerState.timeRemaining = data[timerState.currentSegment].seconds;
                } else {
                    // Timer complete
                    timerState.isRunning = false;
                    clearInterval(timerState.interval);

                    var startBtn = document.getElementById('startBtn');
                    startBtn.textContent = 'Start';

                    // Re-enable inputs
                    var inputs = document.querySelectorAll('.segment input');
                    for (var i = 0; i < inputs.length; i++) {
                        inputs[i].disabled = false;
                    }

                    showNotification('Presentation completed! 🎉');
                    document.getElementById('currentSegment').textContent = 'Presentation Complete';
                    document.getElementById('timeDisplay').textContent = '00:00';
                    return;
                }
            }

            updateDisplay();
        }

        // Toggle timer function
        function toggleTimer() {
            if (!timerState.isRunning && !timerState.isPaused) {
                startTimer();
            } else if (timerState.isRunning) {
                pauseTimer();
            } else if (timerState.isPaused) {
                resumeTimer();
            }
        }

        // Start timer
        function startTimer() {
            var data = getSegmentData();
            var hasValidSegments = false;

            for (var i = 0; i < data.length; i++) {
                if (data[i].seconds > 0) {
                    hasValidSegments = true;
                    break;
                }
            }

            if (!hasValidSegments) {
                showNotification('Please set at least one segment duration', 'warning');
                return;
            }

            timerState.currentSegment = 0;
            timerState.timeRemaining = data[0].seconds;
            timerState.isRunning = true;
            timerState.isPaused = false;

            var startBtn = document.getElementById('startBtn');
            startBtn.textContent = 'Pause';

            // Disable inputs
            var inputs = document.querySelectorAll('.segment input');
            for (var i = 0; i < inputs.length; i++) {
                inputs[i].disabled = true;
            }

            timerState.interval = setInterval(tick, 1000);
            updateDisplay();
            showNotification('Timer started!');
            saveData();
        }

        // Pause timer
        function pauseTimer() {
            timerState.isRunning = false;
            timerState.isPaused = true;
            clearInterval(timerState.interval);

            var startBtn = document.getElementById('startBtn');
            startBtn.textContent = 'Resume';

            showNotification('Timer paused');
        }

        // Resume timer
        function resumeTimer() {
            timerState.isRunning = true;
            timerState.isPaused = false;

            var startBtn = document.getElementById('startBtn');
            startBtn.textContent = 'Pause';

            timerState.interval = setInterval(tick, 1000);
            showNotification('Timer resumed');
        }

        // Reset timer
        function resetTimer() {
            timerState.isRunning = false;
            timerState.isPaused = false;
            timerState.currentSegment = 0;
            timerState.timeRemaining = 0;

            clearInterval(timerState.interval);

            var startBtn = document.getElementById('startBtn');
            startBtn.textContent = 'Start';

            // Re-enable inputs
            var inputs = document.querySelectorAll('.segment input');
            for (var i = 0; i < inputs.length; i++) {
                inputs[i].disabled = false;
            }

            updateDisplay();
            showNotification('Timer reset');
        }

        // Initialize on page load
        window.onload = function() {
            loadData();
            updateDisplay();
            console.log('Simple Timer: Initialized');
        };

        // Fallback initialization
        setTimeout(function() {
            if (typeof timerState !== 'undefined') {
                loadData();
                updateDisplay();
                console.log('Simple Timer: Fallback initialized');
            }
        }, 500);
    </script>
</body>
</html>
