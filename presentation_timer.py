#!/usr/bin/env python3
"""
Presentation Timer - A sequential timer for managing presentation segments
Features: Modern UI with CustomTkinter, progress bars, audio alerts, responsive design
"""

import customtkinter as ctk
from tkinter import messagebox
import threading
import time
import sys
import platform

# Try to import audio libraries based on platform
try:
    if platform.system() == "Windows":
        import winsound
    else:
        import os
except ImportError:
    pass

# Set CustomTkinter appearance
ctk.set_appearance_mode("dark")  # Dark mode
ctk.set_default_color_theme("blue")  # Color theme

class PresentationTimer:
    def __init__(self, root):
        self.root = root
        self.setup_window()
        self.setup_variables()
        self.create_widgets()
        self.setup_layout()

        # Timer state
        self.timer_thread = None
        self.is_running = False
        self.is_paused = False
        self.current_segment = 0
        self.remaining_time = 0
        self.total_segments = 6
        self.segment_start_time = 0  # For progress bar calculation

    def setup_window(self):
        """Configure main window"""
        self.root.title("Presentation Timer")
        self.root.geometry("500x570")
        self.root.minsize(500, 570)

        # Make window responsive
        self.root.grid_rowconfigure(0, weight=1)
        self.root.grid_columnconfigure(0, weight=1)

    def setup_variables(self):
        """Initialize variables"""
        # Segment durations (in minutes) - using regular variables for CustomTkinter
        self.segment_durations = [5.0, 3.5, 10.0, 2.5, 4.0, 1.5]

        # Segment labels
        self.segment_labels = ["Opening", "Introduction", "Main Content", "Discussion", "Q&A", "Closing"]

        # Display variables
        self.current_segment_text = "Ready to Start"
        self.remaining_time_text = "00:00"
        self.progress_text = "Segment 0 of 6"

        # Audio toggle
        self.audio_enabled = True

    def create_widgets(self):
        """Create all GUI widgets"""
        # Main container
        # self.main_frame = ctk.CTkFrame(self.root)

        # Title
        self.title_label = ctk.CTkLabel(self.root, text="🎯 Presentation Timer",
                                       font=ctk.CTkFont(size=24, weight="bold"))

        # Segment configuration frame
        self.config_frame = ctk.CTkFrame(self.root)
        self.config_label = ctk.CTkLabel(self.config_frame, text="⚙️ Segment Configuration",
                                        font=ctk.CTkFont(size=16, weight="bold"))

        # Create segment input widgets
        self.segment_widgets = []
        for i in range(6):
            frame = ctk.CTkFrame(self.config_frame)

            # Segment number label
            num_label = ctk.CTkLabel(frame, text=f"{i+1}.", font=ctk.CTkFont(size=12, weight="bold"))

            # Label entry
            label_entry = ctk.CTkEntry(frame, placeholder_text=f"Segment {i+1} name", width=200)
            label_entry.insert(0, self.segment_labels[i])

            # Duration entry
            duration_entry = ctk.CTkEntry(frame, placeholder_text="5.0", width=80)
            duration_entry.insert(0, str(self.segment_durations[i]))

            # Minutes label
            min_label = ctk.CTkLabel(frame, text="min")

            self.segment_widgets.append({
                'frame': frame,
                'num_label': num_label,
                'label_entry': label_entry,
                'duration_entry': duration_entry,
                'min_label': min_label
            })

        # Timer display frame
        self.display_frame = ctk.CTkFrame(self.root)

        # Current segment display
        self.current_segment_label = ctk.CTkLabel(self.display_frame, text=self.current_segment_text,
                                                 font=ctk.CTkFont(size=32, weight="bold"))

        # Remaining time display (centered)
        self.remaining_time_label = ctk.CTkLabel(self.display_frame, text=self.remaining_time_text,
                                                font=ctk.CTkFont(size=72, weight="bold"))

        # Progress bar (horizontal, resets for each segment)
        self.progress_bar = ctk.CTkProgressBar(self.display_frame, width=400, height=20)
        self.progress_bar.set(0)

        # Progress display
        self.progress_label = ctk.CTkLabel(self.display_frame, text=self.progress_text,
                                          font=ctk.CTkFont(size=24))

        # Controls frame
        self.controls_frame = ctk.CTkFrame(self.root)

        # Control buttons
        self.start_button = ctk.CTkButton(self.controls_frame, text="▶️ Start", font=ctk.CTkFont(size=17, weight="bold"),
                                         command=self.start_timer, width=105, height=40)
        self.pause_button = ctk.CTkButton(self.controls_frame, text="⏸️ Pause", font=ctk.CTkFont(size=17, weight="bold"),
                                         command=self.pause_timer, width=105, height=40, state='disabled')
        self.reset_button = ctk.CTkButton(self.controls_frame, text="🔄 Reset", font=ctk.CTkFont(size=17, weight="bold"),
                                         command=self.reset_timer, width=105, height=40)

        # Audio toggle
        self.audio_checkbox = ctk.CTkCheckBox(self.controls_frame, text="🔊 Audio Alerts")
        self.audio_checkbox.select()  # Default to enabled

        # Upcoming segments frame
        self.upcoming_frame = ctk.CTkFrame(self.main_frame)
        self.upcoming_label = ctk.CTkLabel(self.upcoming_frame, text="📋 Upcoming Segments",
                                          font=ctk.CTkFont(size=14, weight="bold"))
        self.upcoming_text = ctk.CTkTextbox(self.upcoming_frame, height=100, width=400)
        self.upcoming_text.configure(state='disabled')


    def setup_layout(self):
        """Arrange widgets in the layout"""
        # self.main_frame.pack(fill="both", expand=True, padx=5, pady=5)

        # Title
        self.title_label.pack(pady=(0, 5))

        # Segment configuration
        self.config_frame.pack(fill="x", pady=(0, 5), padx=5)
        self.config_label.pack(pady=(10, 5))

        for i, widgets in enumerate(self.segment_widgets):
            widgets['frame'].pack(fill="x", pady=5, padx=5)
            widgets['num_label'].pack(side="left", padx=(5, 5))
            widgets['label_entry'].pack(side="left", padx=(0, 5), fill="x", expand=True)
            widgets['duration_entry'].pack(side="right", padx=(5, 5))
            widgets['min_label'].pack(side="right", padx=(0, 5))

        # Timer display (centered)
        self.display_frame.pack(fill="x", pady=(0, 5), padx=5)
        self.current_segment_label.pack(pady=(15, 5))
        self.remaining_time_label.pack(pady=(0, 5))  # Centered timer
        self.progress_bar.pack(pady=(0, 5))  # Horizontal progress bar
        self.progress_label.pack(pady=(0, 5))

        # Controls
        self.controls_frame.pack(pady=(0, 5))

        # Pack buttons into the button frame
        self.start_button.pack(side="left", padx=5)
        self.pause_button.pack(side="left", padx=5)
        self.reset_button.pack(side="left", padx=5)

        # Audio toggle centered below buttons
        self.audio_checkbox.pack(pady=5, padx=5)

        # Upcoming segments
        self.upcoming_frame.pack(fill="both", expand=True, padx=10, pady=(10, 0))
        self.upcoming_label.pack(pady=(10, 5))
        self.upcoming_text.pack(fill="both", expand=True, padx=10, pady=(0, 10))


    def validate_inputs(self):
        """Validate segment durations and labels"""
        try:
            durations = []
            labels = []

            for i in range(6):
                # Get values from CustomTkinter entries
                duration_text = self.segment_widgets[i]['duration_entry'].get().strip()
                label_text = self.segment_widgets[i]['label_entry'].get().strip()

                if not duration_text:
                    messagebox.showerror("Invalid Input", f"Segment {i+1} duration cannot be empty")
                    return False

                try:
                    duration = float(duration_text)
                except ValueError:
                    messagebox.showerror("Invalid Input", f"Segment {i+1} duration must be a valid number")
                    return False

                if duration <= 0:
                    messagebox.showerror("Invalid Input", f"Segment {i+1} duration must be greater than 0")
                    return False

                if not label_text:
                    messagebox.showerror("Invalid Input", f"Segment {i+1} label cannot be empty")
                    return False

                durations.append(duration)
                labels.append(label_text)

            # Update internal arrays
            self.segment_durations = durations
            self.segment_labels = labels
            return True

        except Exception as e:
            messagebox.showerror("Invalid Input", f"Error validating inputs: {str(e)}")
            return False

    def start_timer(self):
        """Start the presentation timer"""
        if not self.validate_inputs():
            return

        if self.is_paused:
            # Resume from pause
            self.is_paused = False
            self.is_running = True
            self.start_button.configure(state='disabled')
            self.pause_button.configure(state='normal')
        else:
            # Start fresh
            self.current_segment = 0
            self.remaining_time = self.segment_durations[0] * 60  # Convert to seconds
            self.segment_start_time = self.remaining_time  # Store initial time for progress bar
            self.is_running = True
            self.is_paused = False

            self.start_button.configure(state='disabled')
            self.pause_button.configure(state='normal')

            self.update_display()
            self.update_upcoming_segments()

        # Start timer thread
        if self.timer_thread is None or not self.timer_thread.is_alive():
            self.timer_thread = threading.Thread(target=self.timer_loop, daemon=True)
            self.timer_thread.start()

    def pause_timer(self):
        """Pause/resume the timer"""
        if self.is_running:
            self.is_paused = True
            self.is_running = False
            self.start_button.configure(state='normal', text='▶️ Resume')
            self.pause_button.configure(state='disabled')

    def reset_timer(self):
        """Reset the timer to initial state"""
        self.is_running = False
        self.is_paused = False
        self.current_segment = 0
        self.remaining_time = 0
        self.segment_start_time = 0

        self.start_button.configure(state='normal', text='▶️ Start')
        self.pause_button.configure(state='disabled')

        self.current_segment_label.configure(text="Ready to Start")
        self.remaining_time_label.configure(text="00:00")
        self.progress_label.configure(text="Segment 0 of 6")
        self.progress_bar.set(0)

        # Clear upcoming segments display
        self.upcoming_text.configure(state='normal')
        self.upcoming_text.delete("1.0", "end")
        self.upcoming_text.configure(state='disabled')

        # Reset timer color
        self.remaining_time_label.configure(text_color=("white", "white"))

    def timer_loop(self):
        """Main timer loop running in separate thread"""
        while self.is_running and self.current_segment < 6:
            if not self.is_paused:
                if self.remaining_time <= 0:
                    # Move to next segment
                    self.play_alert()
                    self.current_segment += 1

                    if self.current_segment < 6:
                        self.remaining_time = self.segment_durations[self.current_segment] * 60
                        self.segment_start_time = self.remaining_time  # Reset for new segment
                        self.root.after(0, self.update_display)
                        self.root.after(0, self.update_upcoming_segments)
                    else:
                        # All segments completed
                        self.root.after(0, self.timer_completed)
                        break
                else:
                    # Countdown
                    self.remaining_time -= 1
                    self.root.after(0, self.update_display)

                    # Visual warning when time is running low (last 30 seconds)
                    if self.remaining_time <= 30 and self.remaining_time > 0:
                        self.root.after(0, lambda: self.remaining_time_label.configure(text_color='#FF6B6B'))
                    else:
                        self.root.after(0, lambda: self.remaining_time_label.configure(text_color=('white', 'white')))

            time.sleep(1)

    def update_display(self):
        """Update the timer display"""
        if self.current_segment < 6:
            # Current segment info
            segment_name = self.segment_labels[self.current_segment]
            self.current_segment_label.configure(text=f"Current: {segment_name}")

            # Remaining time
            minutes = int(self.remaining_time // 60)
            seconds = int(self.remaining_time % 60)
            self.remaining_time_label.configure(text=f"{minutes:02d}:{seconds:02d}")

            # Progress bar (resets for each segment)
            if self.segment_start_time > 0:
                progress = 1.0 - (self.remaining_time / self.segment_start_time)
                self.progress_bar.set(max(0, min(1, progress)))

            # Progress text
            self.progress_label.configure(text=f"Segment {self.current_segment + 1} of 6")

    def update_upcoming_segments(self):
        """Update the upcoming segments display"""
        self.upcoming_text.configure(state='normal')
        self.upcoming_text.delete("1.0", "end")

        upcoming_text = ""
        for i in range(self.current_segment + 1, 6):
            label = self.segment_labels[i]
            duration = self.segment_durations[i]
            upcoming_text += f"{i + 1}. {label} ({duration} min)\n"

        if not upcoming_text:
            upcoming_text = "No more segments"

        self.upcoming_text.insert("1.0", upcoming_text)
        self.upcoming_text.configure(state='disabled')

    def timer_completed(self):
        """Handle timer completion"""
        self.is_running = False
        self.is_paused = False

        self.current_segment_label.configure(text="🎉 Presentation Complete! 🎉")
        self.remaining_time_label.configure(text="00:00")
        self.progress_label.configure(text="All segments completed")
        self.progress_bar.set(1.0)  # Full progress

        self.start_button.configure(state='normal', text='▶️ Start')
        self.pause_button.configure(state='disabled')

        # Clear upcoming segments
        self.upcoming_text.configure(state='normal')
        self.upcoming_text.delete("1.0", "end")
        self.upcoming_text.insert("1.0", "🎊 Congratulations! Your presentation is complete! 🎊")
        self.upcoming_text.configure(state='disabled')

        # Play completion alert
        self.play_alert()

        # Show completion message
        messagebox.showinfo("Timer Complete", "Your presentation has finished!\nGreat job!")

    def play_alert(self):
        """Play audio alert if enabled"""
        if not self.audio_checkbox.get():
            return

        try:
            if platform.system() == "Windows":
                # Windows system beep
                winsound.Beep(800, 500)  # 800Hz for 500ms
            elif platform.system() == "Darwin":  # macOS
                os.system("afplay /System/Library/Sounds/Glass.aiff")
            else:  # Linux and others
                os.system("paplay /usr/share/sounds/alsa/Front_Left.wav 2>/dev/null || "
                         "aplay /usr/share/sounds/alsa/Front_Left.wav 2>/dev/null || "
                         "echo -e '\\a'")  # Fallback to terminal bell
        except Exception:
            # Fallback to visual alert only
            self.root.bell()  # System bell

    def on_closing(self):
        """Handle window closing"""
        self.is_running = False
        self.root.destroy()


def main():
    """Main application entry point"""
    root = ctk.CTk()
    app = PresentationTimer(root)

    # Handle window closing
    root.protocol("WM_DELETE_WINDOW", app.on_closing)

    # Start the GUI
    root.mainloop()


if __name__ == "__main__":
    main()
