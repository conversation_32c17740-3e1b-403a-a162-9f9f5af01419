#!/usr/bin/env python3
"""
Presentation Timer - A sequential timer for managing presentation segments
Features: Dark mode, audio alerts, responsive design, pause/resume functionality
"""

import tkinter as tk
from tkinter import ttk, messagebox
import threading
import time
import sys
import platform

# Try to import audio libraries based on platform
try:
    if platform.system() == "Windows":
        import winsound
    else:
        import os
except ImportError:
    pass

class PresentationTimer:
    def __init__(self, root):
        self.root = root
        self.setup_window()
        self.setup_variables()
        self.setup_styles()
        self.create_widgets()
        self.setup_layout()
        
        # Timer state
        self.timer_thread = None
        self.is_running = False
        self.is_paused = False
        self.current_segment = 0
        self.remaining_time = 0
        self.total_segments = 6
        
    def setup_window(self):
        """Configure main window"""
        self.root.title("Presentation Timer")
        self.root.geometry("800x900")
        self.root.minsize(600, 700)
        self.root.configure(bg="#121212")
        
        # Make window responsive
        self.root.grid_rowconfigure(0, weight=1)
        self.root.grid_columnconfigure(0, weight=1)
        
    def setup_variables(self):
        """Initialize tkinter variables"""
        # Segment durations (in minutes)
        self.segment_durations = [tk.DoubleVar(value=5.0) for _ in range(6)]
        
        # Segment labels
        default_labels = ["Opening", "Introduction", "Main Content", "Discussion", "Q&A", "Closing"]
        self.segment_labels = [tk.StringVar(value=label) for label in default_labels]
        
        # Display variables
        self.current_segment_var = tk.StringVar(value="Ready to Start")
        self.remaining_time_var = tk.StringVar(value="00:00")
        self.progress_var = tk.StringVar(value="Segment 0 of 6")
        
        # Audio toggle
        self.audio_enabled = tk.BooleanVar(value=True)
        
    def setup_styles(self):
        """Configure ttk styles for dark mode"""
        style = ttk.Style()
        
        # Configure dark theme colors
        style.theme_use('clam')
        
        # Main colors
        bg_color = "#121212"
        surface_color = "#1E1E1E"
        primary_text = "#FFFFFF"
        secondary_text = "#B3B3B3"
        accent_color = "#BB86FC"
        
        # Configure styles
        style.configure('Dark.TFrame', background=bg_color)
        style.configure('Surface.TFrame', background=surface_color, relief='raised', borderwidth=1)
        style.configure('Dark.TLabel', background=bg_color, foreground=primary_text, font=('Segoe UI', 10))
        style.configure('Title.TLabel', background=bg_color, foreground=primary_text, font=('Segoe UI', 16, 'bold'))
        style.configure('Time.TLabel', background=surface_color, foreground=accent_color, font=('Segoe UI', 24, 'bold'))
        style.configure('Segment.TLabel', background=surface_color, foreground=primary_text, font=('Segoe UI', 14))
        style.configure('Dark.TEntry', fieldbackground=surface_color, foreground=primary_text, borderwidth=1)
        style.configure('Dark.TButton', background=surface_color, foreground=primary_text, borderwidth=1)
        style.configure('Dark.TCheckbutton', background=bg_color, foreground=primary_text)
        
        # Button hover effects
        style.map('Dark.TButton',
                 background=[('active', accent_color), ('pressed', '#9965F4')])
                 
    def create_widgets(self):
        """Create all GUI widgets"""
        # Main container
        self.main_frame = ttk.Frame(self.root, style='Dark.TFrame', padding="20")
        
        # Title
        self.title_label = ttk.Label(self.main_frame, text="Presentation Timer", style='Title.TLabel')
        
        # Segment configuration frame
        self.config_frame = ttk.LabelFrame(self.main_frame, text="Segment Configuration", 
                                          style='Surface.TFrame', padding="15")
        
        # Create segment input widgets
        self.segment_widgets = []
        for i in range(6):
            frame = ttk.Frame(self.config_frame, style='Dark.TFrame')
            
            # Segment number label
            num_label = ttk.Label(frame, text=f"{i+1}.", style='Dark.TLabel')
            
            # Label entry
            label_entry = ttk.Entry(frame, textvariable=self.segment_labels[i], 
                                   style='Dark.TEntry', width=15)
            
            # Duration entry
            duration_entry = ttk.Entry(frame, textvariable=self.segment_durations[i], 
                                      style='Dark.TEntry', width=8)
            
            # Minutes label
            min_label = ttk.Label(frame, text="min", style='Dark.TLabel')
            
            self.segment_widgets.append({
                'frame': frame,
                'num_label': num_label,
                'label_entry': label_entry,
                'duration_entry': duration_entry,
                'min_label': min_label
            })
        
        # Timer display frame
        self.display_frame = ttk.LabelFrame(self.main_frame, text="Current Status", 
                                           style='Surface.TFrame', padding="20")
        
        # Current segment display
        self.current_segment_label = ttk.Label(self.display_frame, textvariable=self.current_segment_var,
                                              style='Segment.TLabel')
        
        # Remaining time display
        self.remaining_time_label = ttk.Label(self.display_frame, textvariable=self.remaining_time_var,
                                             style='Time.TLabel')
        
        # Progress display
        self.progress_label = ttk.Label(self.display_frame, textvariable=self.progress_var,
                                       style='Dark.TLabel')
        
        # Controls frame
        self.controls_frame = ttk.Frame(self.main_frame, style='Dark.TFrame')
        
        # Control buttons
        self.start_button = ttk.Button(self.controls_frame, text="Start", 
                                      command=self.start_timer, style='Dark.TButton')
        self.pause_button = ttk.Button(self.controls_frame, text="Pause", 
                                      command=self.pause_timer, style='Dark.TButton', state='disabled')
        self.reset_button = ttk.Button(self.controls_frame, text="Reset", 
                                      command=self.reset_timer, style='Dark.TButton')
        
        # Audio toggle
        self.audio_frame = ttk.Frame(self.main_frame, style='Dark.TFrame')
        self.audio_checkbox = ttk.Checkbutton(self.audio_frame, text="Audio Alerts", 
                                             variable=self.audio_enabled, style='Dark.TCheckbutton')
        
        # Upcoming segments frame
        self.upcoming_frame = ttk.LabelFrame(self.main_frame, text="Upcoming Segments",
                                            style='Surface.TFrame', padding="10")
        self.upcoming_text = tk.Text(self.upcoming_frame, height=4, width=50,
                                    bg="#1E1E1E", fg="#B3B3B3", font=('Segoe UI', 9),
                                    state='disabled', wrap='word')

    def setup_layout(self):
        """Arrange widgets in the layout"""
        self.main_frame.grid(row=0, column=0, sticky="nsew", padx=10, pady=10)

        # Title
        self.title_label.grid(row=0, column=0, pady=(0, 20))

        # Segment configuration
        self.config_frame.grid(row=1, column=0, sticky="ew", pady=(0, 20))

        for i, widgets in enumerate(self.segment_widgets):
            widgets['frame'].grid(row=i, column=0, sticky="ew", pady=2)
            widgets['num_label'].grid(row=0, column=0, padx=(0, 5))
            widgets['label_entry'].grid(row=0, column=1, padx=(0, 10))
            widgets['duration_entry'].grid(row=0, column=2, padx=(0, 5))
            widgets['min_label'].grid(row=0, column=3)

            # Make label entry expandable
            widgets['frame'].grid_columnconfigure(1, weight=1)

        # Timer display
        self.display_frame.grid(row=2, column=0, sticky="ew", pady=(0, 20))
        self.current_segment_label.grid(row=0, column=0, pady=(0, 10))
        self.remaining_time_label.grid(row=1, column=0, pady=(0, 10))
        self.progress_label.grid(row=2, column=0)

        # Controls
        self.controls_frame.grid(row=3, column=0, pady=(0, 20))
        self.start_button.grid(row=0, column=0, padx=(0, 10))
        self.pause_button.grid(row=0, column=1, padx=(0, 10))
        self.reset_button.grid(row=0, column=2)

        # Audio toggle
        self.audio_frame.grid(row=4, column=0, pady=(0, 20))
        self.audio_checkbox.grid(row=0, column=0)

        # Upcoming segments
        self.upcoming_frame.grid(row=5, column=0, sticky="ew")
        self.upcoming_text.grid(row=0, column=0, sticky="ew")

        # Configure grid weights for responsiveness
        self.main_frame.grid_columnconfigure(0, weight=1)
        self.config_frame.grid_columnconfigure(0, weight=1)
        self.upcoming_frame.grid_columnconfigure(0, weight=1)

    def validate_inputs(self):
        """Validate segment durations and labels"""
        try:
            durations = []
            labels = []

            for i in range(6):
                duration = self.segment_durations[i].get()
                label = self.segment_labels[i].get().strip()

                if duration <= 0:
                    messagebox.showerror("Invalid Input", f"Segment {i+1} duration must be greater than 0")
                    return False

                if not label:
                    messagebox.showerror("Invalid Input", f"Segment {i+1} label cannot be empty")
                    return False

                durations.append(duration)
                labels.append(label)

            return True

        except tk.TclError:
            messagebox.showerror("Invalid Input", "Please enter valid numbers for segment durations")
            return False

    def start_timer(self):
        """Start the presentation timer"""
        if not self.validate_inputs():
            return

        if self.is_paused:
            # Resume from pause
            self.is_paused = False
            self.is_running = True
            self.start_button.config(state='disabled')
            self.pause_button.config(state='normal')
        else:
            # Start fresh
            self.current_segment = 0
            self.remaining_time = self.segment_durations[0].get() * 60  # Convert to seconds
            self.is_running = True
            self.is_paused = False

            self.start_button.config(state='disabled')
            self.pause_button.config(state='normal')

            self.update_display()
            self.update_upcoming_segments()

        # Start timer thread
        if self.timer_thread is None or not self.timer_thread.is_alive():
            self.timer_thread = threading.Thread(target=self.timer_loop, daemon=True)
            self.timer_thread.start()

    def pause_timer(self):
        """Pause/resume the timer"""
        if self.is_running:
            self.is_paused = True
            self.is_running = False
            self.start_button.config(state='normal', text='Resume')
            self.pause_button.config(state='disabled')

    def reset_timer(self):
        """Reset the timer to initial state"""
        self.is_running = False
        self.is_paused = False
        self.current_segment = 0
        self.remaining_time = 0

        self.start_button.config(state='normal', text='Start')
        self.pause_button.config(state='disabled')

        self.current_segment_var.set("Ready to Start")
        self.remaining_time_var.set("00:00")
        self.progress_var.set("Segment 0 of 6")

        # Clear upcoming segments display
        self.upcoming_text.config(state='normal')
        self.upcoming_text.delete(1.0, tk.END)
        self.upcoming_text.config(state='disabled')

        # Reset display colors
        self.remaining_time_label.config(style='Time.TLabel')

    def timer_loop(self):
        """Main timer loop running in separate thread"""
        while self.is_running and self.current_segment < 6:
            if not self.is_paused:
                if self.remaining_time <= 0:
                    # Move to next segment
                    self.play_alert()
                    self.current_segment += 1

                    if self.current_segment < 6:
                        self.remaining_time = self.segment_durations[self.current_segment].get() * 60
                        self.root.after(0, self.update_display)
                        self.root.after(0, self.update_upcoming_segments)
                    else:
                        # All segments completed
                        self.root.after(0, self.timer_completed)
                        break
                else:
                    # Countdown
                    self.remaining_time -= 1
                    self.root.after(0, self.update_display)

                    # Visual warning when time is running low (last 30 seconds)
                    if self.remaining_time <= 30 and self.remaining_time > 0:
                        self.root.after(0, lambda: self.remaining_time_label.config(foreground='#FF6B6B'))
                    else:
                        self.root.after(0, lambda: self.remaining_time_label.config(foreground='#BB86FC'))

            time.sleep(1)

    def update_display(self):
        """Update the timer display"""
        if self.current_segment < 6:
            # Current segment info
            segment_name = self.segment_labels[self.current_segment].get()
            self.current_segment_var.set(f"Current: {segment_name}")

            # Remaining time
            minutes = int(self.remaining_time // 60)
            seconds = int(self.remaining_time % 60)
            self.remaining_time_var.set(f"{minutes:02d}:{seconds:02d}")

            # Progress
            self.progress_var.set(f"Segment {self.current_segment + 1} of 6")

    def update_upcoming_segments(self):
        """Update the upcoming segments display"""
        self.upcoming_text.config(state='normal')
        self.upcoming_text.delete(1.0, tk.END)

        upcoming_text = ""
        for i in range(self.current_segment + 1, 6):
            label = self.segment_labels[i].get()
            duration = self.segment_durations[i].get()
            upcoming_text += f"{i + 1}. {label} ({duration} min)\n"

        if not upcoming_text:
            upcoming_text = "No more segments"

        self.upcoming_text.insert(1.0, upcoming_text)
        self.upcoming_text.config(state='disabled')

    def timer_completed(self):
        """Handle timer completion"""
        self.is_running = False
        self.is_paused = False

        self.current_segment_var.set("🎉 Presentation Complete! 🎉")
        self.remaining_time_var.set("00:00")
        self.progress_var.set("All segments completed")

        self.start_button.config(state='normal', text='Start')
        self.pause_button.config(state='disabled')

        # Clear upcoming segments
        self.upcoming_text.config(state='normal')
        self.upcoming_text.delete(1.0, tk.END)
        self.upcoming_text.insert(1.0, "Congratulations! Your presentation is complete.")
        self.upcoming_text.config(state='disabled')

        # Play completion alert
        self.play_alert()

        # Show completion message
        messagebox.showinfo("Timer Complete", "Your presentation has finished!\nGreat job!")

    def play_alert(self):
        """Play audio alert if enabled"""
        if not self.audio_enabled.get():
            return

        try:
            if platform.system() == "Windows":
                # Windows system beep
                winsound.Beep(800, 500)  # 800Hz for 500ms
            elif platform.system() == "Darwin":  # macOS
                os.system("afplay /System/Library/Sounds/Glass.aiff")
            else:  # Linux and others
                os.system("paplay /usr/share/sounds/alsa/Front_Left.wav 2>/dev/null || "
                         "aplay /usr/share/sounds/alsa/Front_Left.wav 2>/dev/null || "
                         "echo -e '\\a'")  # Fallback to terminal bell
        except Exception:
            # Fallback to visual alert only
            self.root.bell()  # System bell

    def on_closing(self):
        """Handle window closing"""
        self.is_running = False
        self.root.destroy()


def main():
    """Main application entry point"""
    root = tk.Tk()
    app = PresentationTimer(root)

    # Handle window closing
    root.protocol("WM_DELETE_WINDOW", app.on_closing)

    # Start the GUI
    root.mainloop()


if __name__ == "__main__":
    main()
