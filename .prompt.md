Create a customizable time tracking interface with the following specifications:

1. Time Segment Configuration
   - Allow users to rename default time segments
   - Implement editable text fields for segment labels
   - Validate input to prevent empty or duplicate names

2. Audio Alert Settings
   - Add a toggle switch component for audio notifications
   - Position the switch prominently in the settings area
   - Include visual feedback for the toggle state
   - Persist the user's preference across sessions

3. Visual Theme Updates
   - Convert the interface to dark mode with these parameters:
     - Background: #121212
     - Surface elements: #1E1E1E
     - Primary text: #FFFFFF
     - Secondary text: #B3B3B3
   - Ensure sufficient contrast ratios (WCAG AA compliance)
   - Apply smooth transitions between UI states

4. Accessibility Requirements
   - Maintain keyboard navigation support
   - Add ARIA labels for interactive elements
   - Include hover states for interactive components

Please implement these changes while preserving all existing functionality and ensuring responsive behavior across device sizes.