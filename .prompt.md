Create a GUI application using customtkinter library that includes:

1. A horizontally centered digital timer display
2. A horizontal progress bar that:
   - Spans the width of the window
   - Automatically resets and starts from 0% when a new time segment begins
   - Updates smoothly to reflect the current progress

Use customtkinter's modern styling and theming capabilities instead of standard tkinter for an enhanced visual appearance. Ensure all UI elements are properly aligned and responsive to window resizing.

Reference the customtkinter documentation at https://github.com/TomSchimansky/CustomTkinter/wiki for proper widget implementation and styling options.

Example widgets to use:
- CTkProgressBar with orientation="horizontal"
- CTkLabel for timer display
- CTkFrame for layout management