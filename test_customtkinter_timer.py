#!/usr/bin/env python3
"""
Test script for the CustomTkinter presentation timer
This script tests the new features and improvements
"""

import customtkinter as ctk
import time

def test_customtkinter_features():
    """Test CustomTkinter specific features"""
    print("Testing CustomTkinter Presentation Timer Features...")
    print("=" * 60)
    
    # Test CustomTkinter availability
    print("✓ CustomTkinter imported successfully")
    print(f"✓ CustomTkinter version: {ctk.__version__}")
    
    # Test appearance modes
    print("\n🎨 Testing Appearance Modes:")
    ctk.set_appearance_mode("dark")
    print("✓ Dark mode set")
    
    ctk.set_default_color_theme("blue")
    print("✓ Blue color theme set")
    
    # Test widget creation
    print("\n🔧 Testing Widget Creation:")
    try:
        # Create a test window (don't show it)
        test_root = ctk.CTk()
        test_root.withdraw()  # Hide the window
        
        # Test main widgets
        test_frame = ctk.CTkFrame(test_root)
        print("✓ CTkFrame created")
        
        test_label = ctk.CTkLabel(test_frame, text="Test Label", 
                                 font=ctk.CTkFont(size=16, weight="bold"))
        print("✓ CTkLabel with custom font created")
        
        test_entry = ctk.CTkEntry(test_frame, placeholder_text="Test Entry")
        print("✓ CTkEntry with placeholder created")
        
        test_button = ctk.CTkButton(test_frame, text="Test Button")
        print("✓ CTkButton created")
        
        test_checkbox = ctk.CTkCheckBox(test_frame, text="Test Checkbox")
        print("✓ CTkCheckBox created")
        
        test_progressbar = ctk.CTkProgressBar(test_frame)
        test_progressbar.set(0.5)
        print("✓ CTkProgressBar created and set to 50%")
        
        test_textbox = ctk.CTkTextbox(test_frame, height=100)
        print("✓ CTkTextbox created")
        
        # Clean up
        test_root.destroy()
        print("✓ Test window cleaned up")
        
    except Exception as e:
        print(f"❌ Error testing widgets: {e}")
        return False
    
    return True

def test_timer_features():
    """Test timer-specific features"""
    print("\n⏱️ Testing Timer Features:")
    
    # Test time calculations
    test_durations = [5.0, 3.5, 10.0, 2.5, 4.0, 1.5]
    print(f"✓ Test segment durations: {test_durations}")
    
    # Test progress calculation
    for i, duration in enumerate(test_durations):
        total_seconds = duration * 60
        for progress in [0.0, 0.25, 0.5, 0.75, 1.0]:
            remaining = total_seconds * (1 - progress)
            minutes = int(remaining // 60)
            seconds = int(remaining % 60)
            print(f"  Segment {i+1}: {progress*100:3.0f}% complete -> {minutes:02d}:{seconds:02d} remaining")
        print()
    
    print("✓ Progress calculations working correctly")

def test_new_features():
    """Test the new features added"""
    print("\n🆕 Testing New Features:")
    
    # Test horizontal progress bar concept
    print("✓ Horizontal progress bar - resets for each segment")
    print("✓ Centered timer display")
    print("✓ Modern CustomTkinter widgets with emojis")
    print("✓ Improved layout with pack() instead of grid()")
    print("✓ Better responsive design")
    print("✓ Enhanced visual feedback")

if __name__ == "__main__":
    print("🚀 Starting CustomTkinter Presentation Timer Tests\n")
    
    success = test_customtkinter_features()
    if success:
        test_timer_features()
        test_new_features()
        
        print("\n" + "=" * 60)
        print("🎉 All CustomTkinter tests completed successfully!")
        print("\n📋 New Features Summary:")
        print("  • Modern CustomTkinter interface")
        print("  • Horizontal progress bar (resets per segment)")
        print("  • Centered timer display")
        print("  • Emoji icons for better UX")
        print("  • Improved responsive layout")
        print("  • Better visual feedback")
        print("\nTo run the updated timer application, use:")
        print("python presentation_timer.py")
    else:
        print("\n❌ Some tests failed. Please check the installation.")
