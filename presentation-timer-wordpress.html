<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Presentation Timer - WordPress Compatible</title>
    <style>
        .wp-presentation-timer * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        .wp-presentation-timer {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif !important;
            background: #121212 !important;
            padding: 20px !important;
            color: #FFFFFF !important;
            border-radius: 12px !important;
            max-width: 400px !important;
            width: 100% !important;
            margin: 0 auto !important;
        }

        .wp-presentation-timer .timer-container {
            background: #1E1E1E !important;
            border-radius: 12px !important;
            box-shadow: 0 4px 20px rgba(0, 0, 0, 0.3) !important;
            padding: 24px !important;
            border: 1px solid #333333 !important;
        }

        .wp-presentation-timer .header {
            text-align: center !important;
            margin-bottom: 24px !important;
        }

        .wp-presentation-timer .header h1 {
            color: #FFFFFF !important;
            font-size: 24px !important;
            font-weight: 600 !important;
            margin-bottom: 8px !important;
        }

        .wp-presentation-timer .settings-section {
            margin-bottom: 24px !important;
            padding: 16px !important;
            background: #2A2A2A !important;
            border-radius: 8px !important;
            border: 1px solid #404040 !important;
        }

        .wp-presentation-timer .settings-header h2 {
            color: #FFFFFF !important;
            font-size: 16px !important;
            font-weight: 600 !important;
            margin-bottom: 12px !important;
        }

        .wp-presentation-timer .setting-item {
            margin-bottom: 8px !important;
        }

        .wp-presentation-timer .setting-label {
            display: flex !important;
            justify-content: space-between !important;
            align-items: center !important;
            font-size: 14px !important;
            color: #B3B3B3 !important;
            font-weight: 500 !important;
        }

        .wp-presentation-timer .setting-label span {
            cursor: default !important;
            user-select: none !important;
        }

        .wp-presentation-timer .segment-inputs {
            margin-bottom: 24px !important;
        }

        .wp-presentation-timer .input-group {
            display: flex !important;
            align-items: center !important;
            margin-bottom: 12px !important;
            gap: 12px !important;
        }

        .wp-presentation-timer .segment-label {
            flex: 1 !important;
            padding: 8px 12px !important;
            border: 2px solid #404040 !important;
            border-radius: 6px !important;
            font-size: 14px !important;
            background: #2A2A2A !important;
            color: #FFFFFF !important;
            transition: all 0.2s !important;
            cursor: text !important;
        }

        .wp-presentation-timer .segment-label:focus {
            outline: none !important;
            border-color: #007bff !important;
            background: #333333 !important;
        }

        .wp-presentation-timer .segment-label:hover {
            border-color: #555555 !important;
        }

        .wp-presentation-timer .time-input {
            width: 80px !important;
            padding: 8px 12px !important;
            border: 2px solid #404040 !important;
            border-radius: 6px !important;
            font-size: 14px !important;
            text-align: center !important;
            background: #2A2A2A !important;
            color: #FFFFFF !important;
            transition: all 0.2s !important;
        }

        .wp-presentation-timer .time-input:focus {
            outline: none !important;
            border-color: #007bff !important;
            background: #333333 !important;
        }

        .wp-presentation-timer .input-group span {
            color: #B3B3B3 !important;
            font-size: 14px !important;
            font-weight: 500 !important;
        }

        .wp-presentation-timer .toggle-switch {
            position: relative !important;
            width: 50px !important;
            height: 24px !important;
            background: #404040 !important;
            border-radius: 12px !important;
            cursor: pointer !important;
            transition: all 0.3s ease !important;
            border: 2px solid transparent !important;
        }

        .wp-presentation-timer .toggle-switch:focus {
            outline: none !important;
            border-color: #007bff !important;
        }

        .wp-presentation-timer .toggle-switch input {
            opacity: 0 !important;
            width: 0 !important;
            height: 0 !important;
            position: absolute !important;
        }

        .wp-presentation-timer .toggle-slider {
            position: absolute !important;
            top: 2px !important;
            left: 2px !important;
            width: 16px !important;
            height: 16px !important;
            background: #B3B3B3 !important;
            border-radius: 50% !important;
            transition: all 0.3s ease !important;
        }

        .wp-presentation-timer .toggle-switch.active {
            background: #007bff !important;
        }

        .wp-presentation-timer .toggle-switch.active .toggle-slider {
            transform: translateX(24px) !important;
            background: #FFFFFF !important;
        }

        .wp-presentation-timer .timer-display {
            text-align: center !important;
            margin-bottom: 24px !important;
            padding: 20px !important;
            background: #2A2A2A !important;
            border-radius: 8px !important;
            border: 2px solid #404040 !important;
        }

        .wp-presentation-timer .current-segment {
            font-size: 16px !important;
            color: #B3B3B3 !important;
            margin-bottom: 8px !important;
            font-weight: 500 !important;
        }

        .wp-presentation-timer .time-remaining {
            font-size: 48px !important;
            font-weight: 700 !important;
            color: #FFFFFF !important;
            margin-bottom: 12px !important;
            font-variant-numeric: tabular-nums !important;
        }

        .wp-presentation-timer .progress-bar {
            width: 100% !important;
            height: 8px !important;
            background: #404040 !important;
            border-radius: 4px !important;
            overflow: hidden !important;
            margin-bottom: 12px !important;
        }

        .wp-presentation-timer .progress-fill {
            height: 100% !important;
            background: linear-gradient(90deg, #007bff, #0056b3) !important;
            transition: width 0.3s ease !important;
            border-radius: 4px !important;
        }

        .wp-presentation-timer .segment-progress {
            display: flex !important;
            gap: 4px !important;
            margin-bottom: 16px !important;
        }

        .wp-presentation-timer .segment-dot {
            flex: 1 !important;
            height: 6px !important;
            background: #404040 !important;
            border-radius: 3px !important;
            transition: background-color 0.3s !important;
        }

        .wp-presentation-timer .segment-dot.completed {
            background: #28a745 !important;
        }

        .wp-presentation-timer .segment-dot.active {
            background: #007bff !important;
        }

        .wp-presentation-timer .controls {
            display: flex !important;
            gap: 12px !important;
            margin-bottom: 16px !important;
        }

        .wp-presentation-timer .btn {
            flex: 1 !important;
            padding: 12px 16px !important;
            border: none !important;
            border-radius: 6px !important;
            font-size: 14px !important;
            font-weight: 600 !important;
            cursor: pointer !important;
            transition: all 0.2s !important;
            text-transform: uppercase !important;
            letter-spacing: 0.5px !important;
        }

        .wp-presentation-timer .btn-primary {
            background: #007bff !important;
            color: white !important;
        }

        .wp-presentation-timer .btn-primary:hover {
            background: #0056b3 !important;
            transform: translateY(-1px) !important;
        }

        .wp-presentation-timer .btn-secondary {
            background: #6c757d !important;
            color: white !important;
        }

        .wp-presentation-timer .btn-secondary:hover {
            background: #545b62 !important;
            transform: translateY(-1px) !important;
        }

        .wp-presentation-timer .btn:disabled {
            opacity: 0.6 !important;
            cursor: not-allowed !important;
        }

        .wp-presentation-timer .notification {
            padding: 12px !important;
            border-radius: 6px !important;
            margin-bottom: 16px !important;
            font-weight: 500 !important;
            text-align: center !important;
            display: none !important;
        }

        .wp-presentation-timer .notification.success {
            background: #1a4d3a !important;
            color: #4ade80 !important;
            border: 1px solid #22c55e !important;
        }

        .wp-presentation-timer .notification.warning {
            background: #4d3319 !important;
            color: #fbbf24 !important;
            border: 1px solid #f59e0b !important;
        }

        .wp-presentation-timer .notification.show {
            display: block !important;
            animation: wpSlideIn 0.3s ease !important;
        }

        @keyframes wpSlideIn {
            from { opacity: 0; transform: translateY(-10px); }
            to { opacity: 1; transform: translateY(0); }
        }

        .wp-presentation-timer .warning {
            color: #dc3545 !important;
        }

        .wp-presentation-timer .warning .progress-fill {
            background: linear-gradient(90deg, #dc3545, #c82333) !important;
        }

        @media (max-width: 480px) {
            .wp-presentation-timer .timer-container {
                padding: 16px !important;
                margin: 10px !important;
            }

            .wp-presentation-timer .time-remaining {
                font-size: 36px !important;
            }

            .wp-presentation-timer .controls {
                flex-direction: column !important;
            }
        }
    </style>
</head>
<body>
    <div class="wp-presentation-timer">
        <div class="timer-container">
            <div class="header">
                <h1>Presentation Timer</h1>
            </div>

            <div class="settings-section">
                <div class="settings-header">
                    <h2>Settings</h2>
                </div>
                <div class="setting-item">
                    <div class="setting-label">
                        <span>Audio Alerts</span>
                        <div class="toggle-switch active" data-role="switch" tabindex="0">
                            <input type="checkbox" data-id="audioToggle" checked>
                            <span class="toggle-slider"></span>
                        </div>
                    </div>
                </div>
            </div>

            <div class="segment-inputs">
                <div class="input-group">
                    <input type="text" class="segment-label" data-id="label1" value="Introduction" maxlength="20">
                    <input type="number" class="time-input" data-id="segment1" min="0.1" max="60" step="0.1" value="5" placeholder="5.0">
                    <span>min</span>
                </div>
                <div class="input-group">
                    <input type="text" class="segment-label" data-id="label2" value="Main Content" maxlength="20">
                    <input type="number" class="time-input" data-id="segment2" min="0.1" max="60" step="0.1" value="15" placeholder="15.0">
                    <span>min</span>
                </div>
                <div class="input-group">
                    <input type="text" class="segment-label" data-id="label3" value="Q&A Session" maxlength="20">
                    <input type="number" class="time-input" data-id="segment3" min="0.1" max="60" step="0.1" value="8" placeholder="8.0">
                    <span>min</span>
                </div>
                <div class="input-group">
                    <input type="text" class="segment-label" data-id="label4" value="Conclusion" maxlength="20">
                    <input type="number" class="time-input" data-id="segment4" min="0.1" max="60" step="0.1" value="2" placeholder="2.0">
                    <span>min</span>
                </div>
            </div>

            <div class="notification" data-id="notification"></div>

            <div class="timer-display">
                <div class="current-segment" data-id="currentSegment">Ready to start</div>
                <div class="time-remaining" data-id="timeRemaining">00:00</div>
                <div class="progress-bar">
                    <div class="progress-fill" data-id="progressFill"></div>
                </div>
                <div class="segment-progress" data-id="segmentProgress">
                    <div class="segment-dot"></div>
                    <div class="segment-dot"></div>
                    <div class="segment-dot"></div>
                    <div class="segment-dot"></div>
                </div>
            </div>

            <div class="controls">
                <button class="btn btn-primary" data-id="startPauseBtn">Start</button>
                <button class="btn btn-secondary" data-id="resetBtn">Reset</button>
            </div>
        </div>
    </div>
    <script>
        (function() {
            'use strict';

            // WordPress-compatible timer class
            function WordPressPresentationTimer() {
                this.segments = [];
                this.segmentLabels = ['Introduction', 'Main Content', 'Q&A Session', 'Conclusion'];
                this.currentSegmentIndex = 0;
                this.timeRemaining = 0;
                this.isRunning = false;
                this.isPaused = false;
                this.interval = null;
                this.audioEnabled = true;
                this.container = null;

                this.init();
            }

            WordPressPresentationTimer.prototype.init = function() {
                try {
                    this.container = document.querySelector('.wp-presentation-timer');
                    if (!this.container) {
                        console.error('WordPress Presentation Timer: Container not found');
                        return;
                    }

                    this.initializeElements();
                    this.loadFromStorage();
                    this.bindEvents();
                    this.updateDisplay();
                    console.log('WordPress Presentation Timer: Initialized successfully');
                } catch (error) {
                    console.error('WordPress Presentation Timer: Initialization failed', error);
                }
            };

            WordPressPresentationTimer.prototype.initializeElements = function() {
                this.segmentInputs = [
                    this.container.querySelector('[data-id="segment1"]'),
                    this.container.querySelector('[data-id="segment2"]'),
                    this.container.querySelector('[data-id="segment3"]'),
                    this.container.querySelector('[data-id="segment4"]')
                ];
                this.labelInputs = [
                    this.container.querySelector('[data-id="label1"]'),
                    this.container.querySelector('[data-id="label2"]'),
                    this.container.querySelector('[data-id="label3"]'),
                    this.container.querySelector('[data-id="label4"]')
                ];
                this.audioToggle = this.container.querySelector('[data-id="audioToggle"]');
                this.toggleSwitch = this.container.querySelector('.toggle-switch');
                this.currentSegmentEl = this.container.querySelector('[data-id="currentSegment"]');
                this.timeRemainingEl = this.container.querySelector('[data-id="timeRemaining"]');
                this.progressFillEl = this.container.querySelector('[data-id="progressFill"]');
                this.segmentProgressEl = this.container.querySelector('[data-id="segmentProgress"]');
                this.startPauseBtn = this.container.querySelector('[data-id="startPauseBtn"]');
                this.resetBtn = this.container.querySelector('[data-id="resetBtn"]');
                this.notificationEl = this.container.querySelector('[data-id="notification"]');

                // Ensure label inputs are editable
                this.labelInputs.forEach(function(input) {
                    if (input) {
                        input.disabled = false;
                        input.readOnly = false;
                    }
                });
            };

            WordPressPresentationTimer.prototype.bindEvents = function() {
                var self = this;

                try {
                    // Button events
                    if (this.startPauseBtn) {
                        this.startPauseBtn.addEventListener('click', function() {
                            self.toggleTimer();
                        });
                    }

                    if (this.resetBtn) {
                        this.resetBtn.addEventListener('click', function() {
                            self.resetTimer();
                        });
                    }

                    // Audio toggle events
                    if (this.toggleSwitch) {
                        this.toggleSwitch.addEventListener('click', function() {
                            self.toggleAudio();
                        });

                        this.toggleSwitch.addEventListener('keydown', function(e) {
                            if (e.key === 'Enter' || e.key === ' ') {
                                e.preventDefault();
                                self.toggleAudio();
                            }
                        });
                    }

                    // Segment duration change events
                    this.segmentInputs.forEach(function(input) {
                        if (input) {
                            input.addEventListener('change', function() {
                                self.saveToStorage();
                            });
                        }
                    });

                    // Label change events with validation
                    this.labelInputs.forEach(function(input, index) {
                        if (input) {
                            input.addEventListener('blur', function() {
                                self.validateLabel(index);
                            });
                            input.addEventListener('change', function() {
                                self.saveToStorage();
                            });
                        }
                    });

                    console.log('WordPress Presentation Timer: Events bound successfully');
                } catch (error) {
                    console.error('WordPress Presentation Timer: Event binding failed', error);
                }
            };

            WordPressPresentationTimer.prototype.loadFromStorage = function() {
                try {
                    var saved = localStorage.getItem('wpPresentationTimer');
                    if (saved) {
                        var data = JSON.parse(saved);
                        var self = this;

                        // Load segment durations
                        this.segmentInputs.forEach(function(input, index) {
                            if (input && data.segments && data.segments[index] !== undefined) {
                                input.value = data.segments[index];
                            }
                        });

                        // Load segment labels
                        if (data.labels) {
                            this.labelInputs.forEach(function(input, index) {
                                if (input && data.labels[index]) {
                                    input.value = data.labels[index];
                                    self.segmentLabels[index] = data.labels[index];
                                }
                            });
                        }

                        // Load audio preference
                        if (data.audioEnabled !== undefined) {
                            this.audioEnabled = data.audioEnabled;
                            if (this.audioToggle) {
                                this.audioToggle.checked = this.audioEnabled;
                            }
                            this.updateToggleState();
                        }
                    }
                } catch (error) {
                    console.error('WordPress Presentation Timer: Failed to load from storage', error);
                }
            };

            WordPressPresentationTimer.prototype.saveToStorage = function() {
                try {
                    var data = {
                        segments: this.segmentInputs.map(function(input) {
                            return input ? (parseFloat(input.value) || 0) : 0;
                        }),
                        labels: this.labelInputs.map(function(input) {
                            return input ? input.value.trim() : '';
                        }),
                        audioEnabled: this.audioEnabled
                    };
                    localStorage.setItem('wpPresentationTimer', JSON.stringify(data));
                } catch (error) {
                    console.error('WordPress Presentation Timer: Failed to save to storage', error);
                }
            };

            WordPressPresentationTimer.prototype.getSegments = function() {
                return this.segmentInputs.map(function(input) {
                    return input ? (parseFloat(input.value) || 0) : 0;
                });
            };

            WordPressPresentationTimer.prototype.getSegmentSeconds = function() {
                return this.segmentInputs.map(function(input) {
                    var minutes = input ? (parseFloat(input.value) || 0) : 0;
                    var seconds = minutes * 60;
                    return Math.round(seconds / 5) * 5;
                });
            };

            WordPressPresentationTimer.prototype.formatTime = function(seconds) {
                var mins = Math.floor(seconds / 60);
                var secs = seconds % 60;
                return mins.toString().padStart(2, '0') + ':' + secs.toString().padStart(2, '0');
            };

            WordPressPresentationTimer.prototype.toggleAudio = function() {
                try {
                    this.audioEnabled = !this.audioEnabled;
                    if (this.audioToggle) {
                        this.audioToggle.checked = this.audioEnabled;
                    }
                    this.updateToggleState();
                    this.saveToStorage();

                    var message = this.audioEnabled ? 'Audio alerts enabled' : 'Audio alerts disabled';
                    this.showNotification(message);
                } catch (error) {
                    console.error('WordPress Presentation Timer: Audio toggle failed', error);
                }
            };

            WordPressPresentationTimer.prototype.updateToggleState = function() {
                if (this.toggleSwitch) {
                    if (this.audioEnabled) {
                        this.toggleSwitch.classList.add('active');
                        this.toggleSwitch.setAttribute('aria-checked', 'true');
                    } else {
                        this.toggleSwitch.classList.remove('active');
                        this.toggleSwitch.setAttribute('aria-checked', 'false');
                    }
                }
            };

            WordPressPresentationTimer.prototype.validateLabel = function(index) {
                var input = this.labelInputs[index];
                if (!input) return false;

                var value = input.value.trim();

                // Check for empty label
                if (!value) {
                    input.value = this.segmentLabels[index];
                    this.showNotification('Segment label cannot be empty', 'warning');
                    return false;
                }

                // Check for duplicate labels
                var self = this;
                var otherLabels = this.labelInputs
                    .map(function(inp, i) {
                        return i !== index && inp ? inp.value.trim() : null;
                    })
                    .filter(function(label) {
                        return label !== null;
                    });

                if (otherLabels.indexOf(value) !== -1) {
                    input.value = this.segmentLabels[index];
                    this.showNotification('Segment labels must be unique', 'warning');
                    return false;
                }

                this.segmentLabels[index] = value;
                return true;
            };

            WordPressPresentationTimer.prototype.showNotification = function(message, type) {
                try {
                    type = type || 'success';
                    if (this.notificationEl) {
                        this.notificationEl.textContent = message;
                        this.notificationEl.className = 'notification ' + type + ' show';

                        var self = this;
                        setTimeout(function() {
                            if (self.notificationEl) {
                                self.notificationEl.classList.remove('show');
                            }
                        }, 3000);
                    }
                } catch (error) {
                    console.error('WordPress Presentation Timer: Notification failed', error);
                }
            };

            WordPressPresentationTimer.prototype.playAlert = function() {
                if (!this.audioEnabled) return;

                try {
                    var audioContext = new (window.AudioContext || window.webkitAudioContext)();
                    var oscillator = audioContext.createOscillator();
                    var gainNode = audioContext.createGain();

                    oscillator.connect(gainNode);
                    gainNode.connect(audioContext.destination);

                    oscillator.frequency.value = 800;
                    oscillator.type = 'sine';

                    gainNode.gain.setValueAtTime(0.3, audioContext.currentTime);
                    gainNode.gain.exponentialRampToValueAtTime(0.01, audioContext.currentTime + 0.5);

                    oscillator.start(audioContext.currentTime);
                    oscillator.stop(audioContext.currentTime + 0.5);
                } catch (error) {
                    console.log('WordPress Presentation Timer: Audio not supported', error);
                }
            };

            WordPressPresentationTimer.prototype.updateDisplay = function() {
                try {
                    var segmentSeconds = this.getSegmentSeconds();

                    if (!this.isRunning && !this.isPaused) {
                        if (this.currentSegmentEl) {
                            this.currentSegmentEl.textContent = 'Ready to start';
                        }
                        if (this.timeRemainingEl) {
                            this.timeRemainingEl.textContent = '00:00';
                        }
                        if (this.progressFillEl) {
                            this.progressFillEl.style.width = '0%';
                        }
                    } else {
                        if (this.currentSegmentEl) {
                            this.currentSegmentEl.textContent = this.segmentLabels[this.currentSegmentIndex];
                        }
                        if (this.timeRemainingEl) {
                            this.timeRemainingEl.textContent = this.formatTime(this.timeRemaining);
                        }

                        if (this.progressFillEl) {
                            var currentSegmentTime = segmentSeconds[this.currentSegmentIndex];
                            var progress = ((currentSegmentTime - this.timeRemaining) / currentSegmentTime) * 100;
                            this.progressFillEl.style.width = Math.max(0, Math.min(100, progress)) + '%';
                        }

                        // Warning when less than 30 seconds remaining
                        if (this.timeRemaining <= 30 && this.timeRemaining > 0) {
                            if (this.timeRemainingEl) {
                                this.timeRemainingEl.classList.add('warning');
                            }
                            if (this.progressFillEl && this.progressFillEl.parentElement) {
                                this.progressFillEl.parentElement.classList.add('warning');
                            }
                        } else {
                            if (this.timeRemainingEl) {
                                this.timeRemainingEl.classList.remove('warning');
                            }
                            if (this.progressFillEl && this.progressFillEl.parentElement) {
                                this.progressFillEl.parentElement.classList.remove('warning');
                            }
                        }
                    }

                    // Update segment progress dots
                    if (this.segmentProgressEl) {
                        var dots = this.segmentProgressEl.children;
                        for (var i = 0; i < dots.length; i++) {
                            dots[i].className = 'segment-dot';
                            if (i < this.currentSegmentIndex) {
                                dots[i].classList.add('completed');
                            } else if (i === this.currentSegmentIndex && this.isRunning) {
                                dots[i].classList.add('active');
                            }
                        }
                    }
                } catch (error) {
                    console.error('WordPress Presentation Timer: Display update failed', error);
                }
            };

            WordPressPresentationTimer.prototype.toggleTimer = function() {
                try {
                    if (!this.isRunning && !this.isPaused) {
                        this.startTimer();
                    } else if (this.isRunning) {
                        this.pauseTimer();
                    } else if (this.isPaused) {
                        this.resumeTimer();
                    }
                } catch (error) {
                    console.error('WordPress Presentation Timer: Toggle timer failed', error);
                }
            };

            WordPressPresentationTimer.prototype.startTimer = function() {
                try {
                    var self = this;

                    // Update segment labels from inputs
                    this.labelInputs.forEach(function(input, index) {
                        if (input && input.value.trim()) {
                            self.segmentLabels[index] = input.value.trim();
                        }
                    });

                    this.segments = this.getSegmentSeconds();
                    var hasValidSegments = this.segments.some(function(s) { return s > 0; });

                    if (!hasValidSegments) {
                        this.showNotification('Please set at least one segment duration', 'warning');
                        return;
                    }

                    this.saveToStorage();
                    this.currentSegmentIndex = 0;
                    this.timeRemaining = this.segments[0];
                    this.isRunning = true;
                    this.isPaused = false;

                    if (this.startPauseBtn) {
                        this.startPauseBtn.textContent = 'Pause';
                    }

                    // Disable inputs during timer operation
                    this.segmentInputs.forEach(function(input) {
                        if (input) input.disabled = true;
                    });
                    this.labelInputs.forEach(function(input) {
                        if (input) input.disabled = true;
                    });

                    this.interval = setInterval(function() {
                        self.tick();
                    }, 1000);

                    this.updateDisplay();
                    this.showNotification('Timer started!');
                } catch (error) {
                    console.error('WordPress Presentation Timer: Start timer failed', error);
                }
            };

            WordPressPresentationTimer.prototype.pauseTimer = function() {
                try {
                    this.isRunning = false;
                    this.isPaused = true;
                    clearInterval(this.interval);

                    if (this.startPauseBtn) {
                        this.startPauseBtn.textContent = 'Resume';
                    }

                    this.showNotification('Timer paused');
                } catch (error) {
                    console.error('WordPress Presentation Timer: Pause timer failed', error);
                }
            };

            WordPressPresentationTimer.prototype.resumeTimer = function() {
                try {
                    var self = this;
                    this.isRunning = true;
                    this.isPaused = false;

                    if (this.startPauseBtn) {
                        this.startPauseBtn.textContent = 'Pause';
                    }

                    this.interval = setInterval(function() {
                        self.tick();
                    }, 1000);

                    this.showNotification('Timer resumed');
                } catch (error) {
                    console.error('WordPress Presentation Timer: Resume timer failed', error);
                }
            };

            WordPressPresentationTimer.prototype.resetTimer = function() {
                try {
                    this.isRunning = false;
                    this.isPaused = false;
                    this.currentSegmentIndex = 0;
                    this.timeRemaining = 0;

                    clearInterval(this.interval);

                    if (this.startPauseBtn) {
                        this.startPauseBtn.textContent = 'Start';
                    }

                    // Re-enable inputs
                    this.segmentInputs.forEach(function(input) {
                        if (input) input.disabled = false;
                    });
                    this.labelInputs.forEach(function(input) {
                        if (input) input.disabled = false;
                    });

                    this.updateDisplay();
                    this.showNotification('Timer reset');
                } catch (error) {
                    console.error('WordPress Presentation Timer: Reset timer failed', error);
                }
            };

            WordPressPresentationTimer.prototype.tick = function() {
                try {
                    this.timeRemaining--;

                    if (this.timeRemaining <= 0) {
                        this.playAlert();

                        if (this.currentSegmentIndex < this.segments.length - 1) {
                            this.showNotification(this.segmentLabels[this.currentSegmentIndex] + ' completed!');

                            this.currentSegmentIndex++;
                            this.timeRemaining = this.segments[this.currentSegmentIndex];
                        } else {
                            this.isRunning = false;
                            clearInterval(this.interval);

                            if (this.startPauseBtn) {
                                this.startPauseBtn.textContent = 'Start';
                            }

                            // Re-enable inputs
                            this.segmentInputs.forEach(function(input) {
                                if (input) input.disabled = false;
                            });
                            this.labelInputs.forEach(function(input) {
                                if (input) input.disabled = false;
                            });

                            this.showNotification('Presentation completed! 🎉');

                            if (this.currentSegmentEl) {
                                this.currentSegmentEl.textContent = 'Presentation Complete';
                            }
                            if (this.timeRemainingEl) {
                                this.timeRemainingEl.textContent = '00:00';
                            }
                            return;
                        }
                    }

                    this.updateDisplay();
                } catch (error) {
                    console.error('WordPress Presentation Timer: Tick failed', error);
                }
            };

            // Initialize multiple ways for WordPress compatibility
            function initializeTimer() {
                try {
                    new WordPressPresentationTimer();
                } catch (error) {
                    console.error('WordPress Presentation Timer: Failed to initialize', error);
                }
            }

            // Try multiple initialization methods
            if (document.readyState === 'loading') {
                document.addEventListener('DOMContentLoaded', initializeTimer);
            } else {
                initializeTimer();
            }

            // Fallback initialization
            window.addEventListener('load', function() {
                setTimeout(initializeTimer, 100);
            });

        })();
    </script>
</body>
</html>
